/**
 * Description Toggle System
 * Provides show more/less functionality for truncated content
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

(function(window, $) {
    'use strict';

    /**
     * Description toggle manager
     */
    var DescriptionToggle = {
        
        // Configuration
        config: {
            containerSelector: '.description-content',
            truncatedClass: 'description-truncated',
            expandedClass: 'description-expanded',
            toggleClass: 'description-toggle',
            showMoreText: 'Mostra di più',
            showLessText: 'Mostra meno',
            animationDuration: 300
        },

        // Instance counter for unique IDs
        instanceCounter: 0,

        /**
         * Initialize description toggle for elements
         * 
         * @param {string|jQuery} selector - Selector for containers to process
         * @param {object} options - Configuration options
         */
        init: function(selector, options) {
            this.config = $.extend({}, this.config, options || {});
            
            var self = this;
            $(selector || this.config.containerSelector).each(function() {
                self._initializeContainer($(this));
            });
        },

        /**
         * Initialize a single container
         * 
         * @private
         * @param {jQuery} $container - Container element
         */
        _initializeContainer: function($container) {
            if ($container.data('description-toggle-initialized')) {
                return; // Already initialized
            }

            var htmlContent = $container.html().trim();
            var maxLength = parseInt($container.data('truncate-length')) || 300;

            // Validation and edge case handling
            if (!htmlContent || htmlContent === '<p><br></p>' || htmlContent === '<br>') {
                return; // No meaningful content
            }

            if (!window.ContentTruncation) {
                console.warn('ContentTruncation utility not available');
                return;
            }

            // Validate maxLength
            if (maxLength < 10) {
                console.warn('Truncation length too small, using minimum of 50');
                maxLength = 50;
            } else if (maxLength > 5000) {
                console.warn('Truncation length too large, using maximum of 5000');
                maxLength = 5000;
            }

            var result = window.ContentTruncation.truncateHtml(htmlContent, maxLength);

            if (!result.isTruncated || result.visibleLength <= maxLength * 0.8) {
                // Content doesn't need truncation or is too close to the limit
                return;
            }

            this._setupTruncatedContent($container, result);
            $container.data('description-toggle-initialized', true);
        },

        /**
         * Setup truncated content with toggle functionality
         * 
         * @private
         * @param {jQuery} $container - Container element
         * @param {object} truncationResult - Result from ContentTruncation.truncateHtml
         */
        _setupTruncatedContent: function($container, truncationResult) {
            var instanceId = 'desc-toggle-' + (++this.instanceCounter);
            var self = this;

            // Create the truncated view
            var $truncatedContent = $('<div>')
                .addClass(this.config.truncatedClass)
                .html(truncationResult.truncated + '... ')
                .append(
                    $('<a>')
                        .attr('href', '#' + instanceId)
                        .addClass('text-primary fw-bold text-decoration-none ' + this.config.toggleClass)
                        .attr('data-bs-toggle', 'collapse')
                        .attr('aria-expanded', 'false')
                        .attr('aria-controls', instanceId)
                        .text(this.config.showMoreText)
                );

            // Create the expanded view (initially collapsed)
            var $expandedContent = $('<div>')
                .addClass('collapse ' + this.config.expandedClass)
                .attr('id', instanceId)
                .html(
                    $('<div>')
                        .html(truncationResult.remaining)
                        .append(' ')
                        .append(
                            $('<a>')
                                .attr('href', '#' + instanceId)
                                .addClass('text-primary fw-bold text-decoration-none ' + this.config.toggleClass)
                                .attr('data-bs-toggle', 'collapse')
                                .attr('aria-expanded', 'true')
                                .attr('aria-controls', instanceId)
                                .text(this.config.showLessText)
                        )
                );

            // Replace container content
            $container.empty().append($truncatedContent).append($expandedContent);

            // Setup event handlers
            this._setupEventHandlers($container, instanceId);
        },

        /**
         * Setup event handlers for toggle functionality
         * 
         * @private
         * @param {jQuery} $container - Container element
         * @param {string} instanceId - Unique instance ID
         */
        _setupEventHandlers: function($container, instanceId) {
            var self = this;
            var $collapseElement = $('#' + instanceId);
            var $truncatedContent = $container.find('.' + this.config.truncatedClass);

            // Handle collapse show event
            $collapseElement.on('show.bs.collapse', function() {
                $truncatedContent.find('.' + self.config.toggleClass).hide();
                $truncatedContent.find('.dots, .ellipsis').hide();
            });

            // Handle collapse hide event
            $collapseElement.on('hide.bs.collapse', function() {
                $truncatedContent.find('.' + self.config.toggleClass).show();
                $truncatedContent.find('.dots, .ellipsis').show();
            });

            // Handle collapse shown event (fully expanded)
            $collapseElement.on('shown.bs.collapse', function() {
                // Scroll to ensure the "show less" link is visible
                var $showLessLink = $collapseElement.find('.' + self.config.toggleClass);
                if ($showLessLink.length) {
                    var elementTop = $showLessLink.offset().top;
                    var windowHeight = $(window).height();
                    var scrollTop = $(window).scrollTop();
                    
                    // Only scroll if the link is not visible
                    if (elementTop > scrollTop + windowHeight - 100) {
                        $('html, body').animate({
                            scrollTop: elementTop - windowHeight + 100
                        }, self.config.animationDuration);
                    }
                }
            });
        },

        /**
         * Refresh all initialized toggles (useful for dynamic content)
         */
        refresh: function() {
            var self = this;
            $(this.config.containerSelector).each(function() {
                var $container = $(this);
                if ($container.data('description-toggle-initialized')) {
                    $container.removeData('description-toggle-initialized');
                    self._initializeContainer($container);
                }
            });
        },

        /**
         * Destroy toggle functionality for a container
         * 
         * @param {string|jQuery} selector - Container selector
         */
        destroy: function(selector) {
            $(selector || this.config.containerSelector).each(function() {
                var $container = $(this);
                $container.removeData('description-toggle-initialized');
                $container.find('.collapse').off('.bs.collapse');
                // Note: Original content would need to be stored to fully restore
            });
        },

        /**
         * Get configuration
         * 
         * @returns {object} Current configuration
         */
        getConfig: function() {
            return $.extend({}, this.config);
        },

        /**
         * Update configuration
         * 
         * @param {object} newConfig - New configuration options
         */
        updateConfig: function(newConfig) {
            this.config = $.extend({}, this.config, newConfig);
        }
    };

    // Export to global scope
    window.DescriptionToggle = DescriptionToggle;

    // Auto-initialize on DOM ready if jQuery is available
    if ($ && $.fn.ready) {
        $(document).ready(function() {
            // Auto-initialize elements with data-auto-truncate attribute
            $('[data-auto-truncate="true"]').each(function() {
                DescriptionToggle.init(this);
            });
        });
    }

})(window, window.jQuery);
