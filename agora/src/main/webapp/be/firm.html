{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Configurazione AMBIENTE</title>    
<!-- SCRIPTS -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/firm.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
<a id="dataReloadPagesIdentifierUri" style="display: none" href="{{ paths('FIRM_RELOAD_PAGES_IDENTIFIER') }}"></a>
<a id="dataReloadEventsIdentifierUri" style="display: none" href="{{ paths('FIRM_RELOAD_EVENTS_IDENTIFIER') }}"></a>

<!-- Page content -->
<div class="page-content">

    <!-- Main content -->
    <div class="content-wrapper">
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                {% if success %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <a href="{{ paths('FIRM') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                            <span>Dati memorizzati correttamente</span>
                        </div>
                    </div>
                </div>
                {% elseif error %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <a href="{{ paths('FIRM') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                            <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Horizontal form options -->
                <!-- Firm info -->
                <form id="form-edit-firm" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('FIRM_SAVE') }}">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Configurazione AMBIENTE</h5>
                        </div>
                        <div class="panel-body">

                            <input type="hidden" name="id" value="{{ firm.id }}">

                            <legend class="text-bold"><i class="icon-address-book"></i> CONTATTI</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email richieste da sito:</label>
                                <div class="col-lg-9">
                                    <input type="email" name="siteEmail" class="form-control" placeholder="Es. <EMAIL>" value="{{ firm.siteEmail }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email richieste dallo shop:</label>
                                <div class="col-lg-9">
                                    <input type="email" name="shopEmail" class="form-control" placeholder="Es. <EMAIL>" value="{{ firm.shopEmail }}">
                                </div>
                            </div>

                            <legend class="text-bold"><i class="icon-alert"></i> NOTIFICHE</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Ricevi notifiche dallo shop:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select" name="shopNotification">
                                            <option value="never" {{ firm.shopNotification == 'never' ? 'selected' : '' }}>Mai</option>
                                            <option value="always" {{ firm.shopNotification == 'always' ? 'selected' : '' }}>Sempre</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email notifiche dallo shop:</label>
                                <div class="col-lg-9">
                                    <input type="email" name="shopNotificationEmail" class="form-control" placeholder="Es. <EMAIL>" value="{{ firm.shopNotificationEmail }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Invia notifiche al cliente:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select" name="customerNotification">
                                            <option value="never" {{ firm.customerNotification == 'never' ? 'selected' : '' }}>Mai</option>
                                            <option value="always" {{ firm.customerNotification == 'always' ? 'selected' : '' }}>Sempre</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Abilita notifiche email:</label>
                                <div class="col-lg-9">
                                    <div class="checkbox checkbox-switchery">
                                        <label>
                                            <input type="checkbox" name="mailNotificationEnabled" class="switchery" {{ firm.mailNotificationEnabled ? 'checked' : '' }}>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <legend class="text-bold"><i class="icon-gear"></i> INTEGRAZIONE</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Apikey:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="apikey" class="form-control" placeholder="Es. dd4f1bc5-4688-4c6f-afa9-15395ce5c7ae" value="{{ firm.apikey }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Seconda Apikey:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="secondApikey" class="form-control" placeholder="Es. dd4f1bc5-4688-4c6f-afa9-15395ce5c7aa" value="{{ firm.secondApikey }}">
                                </div>
                            </div>

                            <legend class="text-bold"><i class="icon-gear"></i> USO INTERNO</legend>
                            <div class="form-group">
                                <button id="btn-identifier-page" class="btn heading-btn btn-primary">Ricarica Pagine TitleIdentifier</button>
                                <button id="btn-identifier-event" class="btn heading-btn btn-primary">Ricarica Eventi TitleIdentifier</button>
                            </div>

                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    <button id="btn-save" type="submit" class="btn heading-btn btn-primary">Salva<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>

    </div>
    <!-- /main content -->

</div>
<!-- /page content -->
{% endblock %}
