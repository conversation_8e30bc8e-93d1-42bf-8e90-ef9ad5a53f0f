/**
 * Description Truncation Styles
 * Provides styling for truncated content and toggle functionality
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

/* Description content container */
.description-content {
    line-height: 1.6;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Truncated content styling */
.description-truncated {
    position: relative;
}

/* Expanded content styling */
.description-expanded {
    margin-top: 0;
}

/* Toggle links styling */
.description-toggle {
    font-weight: 600;
    text-decoration: none !important;
    transition: color 0.2s ease;
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
    margin: 0;
    display: inline;
}

.description-toggle:hover {
    text-decoration: underline !important;
    opacity: 0.8;
}

.description-toggle:focus {
    outline: 2px solid rgba(0, 123, 255, 0.25);
    outline-offset: 2px;
}

/* Smooth transitions for collapse/expand */
.description-expanded.collapsing {
    transition: height 0.35s ease;
}

.description-expanded.collapse:not(.show) {
    display: none;
}

.description-expanded.collapse.show {
    display: block;
}

/* Ellipsis styling */
.description-truncated .dots,
.description-truncated .ellipsis {
    color: #6c757d;
    font-weight: normal;
}

/* Preview styling for edit mode */
.description-preview {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
    padding: 1rem;
    margin-top: 0.5rem;
}

.description-preview h6 {
    margin-bottom: 0.75rem;
    color: #0d6efd;
    font-size: 0.875rem;
    font-weight: 600;
}

.description-preview .preview-content {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.75rem;
    min-height: 3rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.description-preview .preview-info {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.5rem;
    display: block;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .description-content {
        font-size: 0.9rem;
    }
    
    .description-toggle {
        font-size: 0.85rem;
    }
    
    .description-preview {
        padding: 0.75rem;
    }
    
    .description-preview .preview-content {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
}

/* Dark mode support (if applicable) */
@media (prefers-color-scheme: dark) {
    .description-preview {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .description-preview .preview-content {
        background-color: #1a202c;
        border-color: #2d3748;
        color: #e2e8f0;
    }
    
    .description-preview h6 {
        color: #63b3ed;
    }
    
    .description-truncated .dots,
    .description-truncated .ellipsis {
        color: #a0aec0;
    }
}

/* Animation for smooth content transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.description-expanded.show {
    animation: fadeIn 0.3s ease-out;
}

/* Loading state (if needed) */
.description-content.loading {
    opacity: 0.6;
    pointer-events: none;
}

.description-content.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
