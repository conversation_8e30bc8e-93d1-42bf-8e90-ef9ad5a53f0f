package com.agora.commons;

import com.agora.dao.FirmDao;
import com.agora.dao.UserDao;
import com.agora.pojo.Firm;
import com.agora.pojo.User;
import com.agora.pojo.types.MailnotificationStatusType;
import com.agora.util.TimeUtils;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Scheduler for automated mail notifications
 * Runs hourly checks and specific 8AM execution
 * 
 * <AUTHOR>
 */
public class MailNotificationScheduler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(MailNotificationScheduler.class.getName());
    private static final String APIKEY_USER = "<EMAIL>";
    
    private static ScheduledExecutorService scheduler;
    private static boolean isRunning = false;
    
    /**
     * Start the mail notification scheduler
     */
    public static synchronized void start() {
        if (isRunning) {
            LOGGER.warn("Mail notification scheduler is already running");
            return;
        }
        
        LOGGER.info("Starting mail notification scheduler");
        
        scheduler = Executors.newScheduledThreadPool(1);
        
        // Schedule hourly checks
        scheduler.scheduleAtFixedRate(
            MailNotificationScheduler::performHourlyCheck,
            0, // Initial delay
            1, // Period
            TimeUnit.HOURS
        );
        
        isRunning = true;
        LOGGER.info("Mail notification scheduler started successfully");
    }
    
    /**
     * Stop the mail notification scheduler
     */
    public static synchronized void stop() {
        if (!isRunning) {
            LOGGER.warn("Mail notification scheduler is not running");
            return;
        }
        
        LOGGER.info("Stopping mail notification scheduler");
        
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                // Wait up to 30 seconds for existing tasks to complete
                if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                    // Wait up to 10 more seconds for tasks to respond to being cancelled
                    if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                        LOGGER.error("Scheduler did not terminate gracefully");
                    }
                }
            } catch (InterruptedException ex) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        isRunning = false;
        LOGGER.info("Mail notification scheduler stopped");
    }
    
    /**
     * Check if the scheduler is running
     */
    public static boolean isRunning() {
        return isRunning;
    }
    
    /**
     * Perform hourly check for notifications
     */
    private static void performHourlyCheck() {
        try {
            LOGGER.debug("Performing hourly notification check");
            
            // Check if mail notifications are enabled
            if (!isMailNotificationEnabled()) {
                LOGGER.debug("Mail notifications are disabled, skipping check");
                return;
            }
            
            // Check if another notification process is already running
            if (MailnotificationCommons.alignmentProcessing()) {
                LOGGER.debug("Another notification process is running, skipping check");
                return;
            }
            
            // Check and send daily notifications if needed
            if (MailnotificationCommons.shouldSendNotification("day")) {
                LOGGER.info("Sending automated daily notifications");
                processAutomatedNotifications("day");
            }
            
            // Check and send weekly notifications if needed
            if (MailnotificationCommons.shouldSendNotification("week")) {
                LOGGER.info("Sending automated weekly notifications");
                processAutomatedNotifications("week");
            }
            
        } catch (Exception ex) {
            LOGGER.error("Error during hourly notification check", ex);
        }
    }
    
    /**
     * Perform 8AM check for notifications
     */
    private static void perform8AMCheck() {
        try {
            LOGGER.info("Performing 8AM notification check");
            
            // Check if mail notifications are enabled
            if (!isMailNotificationEnabled()) {
                LOGGER.info("Mail notifications are disabled, skipping 8AM check");
                return;
            }
            
            // Check if another notification process is already running
            if (MailnotificationCommons.alignmentProcessing()) {
                LOGGER.info("Another notification process is running, skipping 8AM check");
                return;
            }
            
            // Always check both daily and weekly at 8AM
            boolean dailySent = false;
            boolean weeklySent = false;
            
            if (MailnotificationCommons.shouldSendNotification("day")) {
                LOGGER.info("Sending automated daily notifications at 8AM");
                dailySent = processAutomatedNotifications("day");
            }
            
            if (MailnotificationCommons.shouldSendNotification("week")) {
                LOGGER.info("Sending automated weekly notifications at 8AM");
                weeklySent = processAutomatedNotifications("week");
            }
            
            if (!dailySent && !weeklySent) {
                LOGGER.info("No notifications needed at 8AM check");
            }
            
        } catch (Exception ex) {
            LOGGER.error("Error during 8AM notification check", ex);
        }
    }
    
    /**
     * Process automated notifications for a given frequency
     */
    private static boolean processAutomatedNotifications(String frequency) {
        try {
            // Get the API user for automated processing
            User apiUser = UserDao.loadUserByUsername(APIKEY_USER);
            if (apiUser == null) {
                LOGGER.error("Could not load API user: {}", APIKEY_USER);
                return false;
            }
            
            // Start the notification process
            if (!MailnotificationCommons.alignmentStart(MailnotificationCommons.OperationType.sendnotification, apiUser.getId())) {
                LOGGER.error("Could not start notification process for frequency: {}", frequency);
                return false;
            }
            
            // Process the notifications with time checking enabled
            boolean success = MailnotificationCommons.processMailNotifications(null, apiUser.getId(), apiUser.getUsername(), frequency, true);
            
            if (success) {
                MailnotificationCommons.alignmentFinish();
                LOGGER.info("Successfully processed {} notifications", frequency);
            } else {
                MailnotificationCommons.alignmentFinish(MailnotificationStatusType.error);
                LOGGER.error("Failed to process {} notifications", frequency);
            }
            
            return success;
            
        } catch (Exception ex) {
            LOGGER.error("Error processing automated notifications for frequency: " + frequency, ex);
            try {
                MailnotificationCommons.alignmentFinish(MailnotificationStatusType.error);
            } catch (Exception finishEx) {
                LOGGER.error("Error finishing alignment after exception", finishEx);
            }
            return false;
        }
    }
    
    /**
     * Check if mail notifications are enabled in the system
     */
    private static boolean isMailNotificationEnabled() {
        try {
            Firm firm = FirmDao.loadFirm();
            return firm != null && BooleanUtils.isTrue(firm.getMailNotificationEnabled());
        } catch (Exception ex) {
            LOGGER.error("Error checking if mail notifications are enabled", ex);
            return false;
        }
    }
    
    /**
     * Calculate initial delay to next 8AM
     */
    private static long calculateInitialDelay8AM() {
        LocalTime now = LocalTime.now(ZoneId.systemDefault());
        LocalTime target = LocalTime.of(8, 0); // 8:00 AM
        
        long hoursUntil8AM;
        if (now.isBefore(target)) {
            // 8AM is today
            hoursUntil8AM = target.toSecondOfDay() / 3600 - now.toSecondOfDay() / 3600;
        } else {
            // 8AM is tomorrow
            hoursUntil8AM = 24 - (now.toSecondOfDay() / 3600 - target.toSecondOfDay() / 3600);
        }
        
        LOGGER.info("Calculated {} hours until next 8AM check", hoursUntil8AM);
        return hoursUntil8AM;
    }
}
